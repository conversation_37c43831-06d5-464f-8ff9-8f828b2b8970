<template>
	<view class="container">
		<!-- 顶部标题 -->
		<!-- <view class="header">
      <text class="header-title">{{formTitle}}</text>
      <view class="status-badge" :class="'status-' + nodeStatus">
        <text class="status-text">{{getStatusText(nodeStatus)}}</text>
      </view>
    </view> -->

		<!-- 表单内容 -->
		<view class="form-container">
			<view class="form-section" v-for="(section, sectionIndex) in formConfig.sections" :key="sectionIndex">
				<view class="section-header">
					<view class="section-title">{{section.title}}</view>
					<view class="section-info" v-if="section.nodeState">
						<text class="node-state"
							:class="'state-' + getStateClass(section.nodeState)">{{section.nodeState}}</text>
						<text class="node-id" v-if="section.nodeId">ID: {{section.nodeId}}</text>
					</view>
					<view class="section-responsible" v-if="section.realName">
						<text class="responsible-label">负责人：</text>
						<text class="responsible-name">{{section.realName}}</text>
					</view>
				</view>

				<view class="form-fields">
					<view v-for="(field, fieldIndex) in section.fields" :key="fieldIndex" class="field-item">
						<!-- 文本输入框 -->
						<view v-if="field.type === 'input'" class="field-wrapper">
							<text class="field-label">
								{{field.label}}
								<text v-if="field.required" class="required-mark">*</text>
							</text>
							<input class="field-input" :type="field.inputType || 'text'" :placeholder="field.placeholder" v-model="formData[field.key]" :disabled="field.disabled || nodeStatus === 'completed'" />
						</view>

						<!-- 多行文本 -->
						<view v-else-if="field.type === 'textarea'" class="field-wrapper">
							<text class="field-label">
								{{field.label}}
								<text v-if="field.required" class="required-mark">*</text>
							</text>
							<textarea class="field-textarea" :placeholder="field.placeholder"
								v-model="formData[field.key]" :disabled="field.disabled || nodeStatus === 'completed'"
								:maxlength="field.maxLength || 500" />
						</view>

						<!-- 选择器 -->
						<view v-else-if="field.type === 'picker'" class="field-wrapper">
							<text class="field-label">
								{{field.label}}
								<text v-if="field.required" class="required-mark">*</text>
							</text>
							<picker :range="field.options" :range-key="field.optionKey || 'label'"
								@change="handlePickerChange($event, field.key)" :disabled="field.disabled || nodeStatus === 'completed'">
								<view class="field-picker" :class="{'disabled': field.disabled || nodeStatus === 'completed'}">
									<text class="picker-text" :class="{'placeholder': !formData[field.key]}">
										{{getPickerDisplayText(field, formData[field.key]) || field.placeholder}}
									</text>
									<text class="picker-arrow">▼</text>
								</view>
							</picker>
						</view>

						<!-- 数字输入 -->
						<view v-else-if="field.type === 'number'" class="field-wrapper">
							<text class="field-label">
								{{field.label}}
								<text v-if="field.required" class="required-mark">*</text>
							</text>
							<input class="field-input" type="digit" :placeholder="field.placeholder"
								v-model="formData[field.key]" :disabled="field.disabled || nodeStatus === 'completed'" />
						</view>

						<!-- 日期选择 -->
						<view v-else-if="field.type === 'date'" class="field-wrapper">
							<text class="field-label">
								{{field.label}}
								<text v-if="field.required" class="required-mark">*</text>
							</text>
							<picker mode="date" @change="handleDateChange($event, field.key)"
								:disabled="field.disabled || nodeStatus === 'completed'">
								<view class="field-picker" :class="{'disabled': field.disabled || nodeStatus === 'completed'}">
									<text class="picker-text" :class="{'placeholder': !formData[field.key]}">
										{{formData[field.key] || field.placeholder}}
									</text>
									<text class="picker-arrow">📅</text>
								</view>
							</picker>
						</view>

						<!-- 图片上传 -->
						<view v-else-if="field.type === 'image'" class="field-wrapper">
							<text class="field-label">
								{{field.label}}
								<text v-if="field.required" class="required-mark">*</text>
							</text>
							<view class="image-upload-container">
								<!-- 图片列表 -->
								<view class="image-list">
									<view v-for="(image, imageIndex) in getImageList(field.key)" :key="imageIndex" class="image-item">
										<image :src="image" class="thumbnail-image" mode="aspectFill" @tap="previewImage(image, getImageList(field.key))"></image>
										<view class="image-delete" @tap="removeImageByIndex(field.key, imageIndex)"
											v-if="nodeStatus !== 'completed'">
											<text class="delete-icon">×</text>
										</view>
									</view>
									<!-- 添加图片按钮 -->
									<view class="add-image-btn" @tap="chooseImage(field.key)"
										v-if="getImageList(field.key).length < 9 && nodeStatus !== 'completed'">
										<text class="add-icon">+</text>
										<text class="add-text">添加图片</text>
									</view>
								</view>
								<!-- 图片数量提示 -->
								<view class="image-count" v-if="getImageList(field.key).length > 0">
									<text class="count-text">已上传 {{getImageList(field.key).length}} 张图片</text>
								</view>
							</view>
						</view>

						<!-- 视频上传 -->
						<view v-else-if="field.type === 'video'" class="field-wrapper">
							<text class="field-label">
								{{field.label}}
								<text v-if="field.required" class="required-mark">*</text>
							</text>
							<view class="video-upload-container">
								<view v-if="formData[field.key]" class="video-preview">
									<video :src="formData[field.key]" class="preview-video" controls></video>
									<view class="video-actions" v-if="nodeStatus !== 'completed'">
										<text class="action-btn delete-btn" @tap="removeVideo(field.key)">删除</text>
									</view>
								</view>
								<view v-else-if="nodeStatus !== 'completed'" class="upload-placeholder" @tap="chooseVideo(field.key)">
									<text class="upload-icon">🎥</text>
									<text class="upload-text">{{field.placeholder}}</text>
								</view>
							</view>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部操作按钮 -->
		<view class="footer-actions">
			<!-- 已完成状态：只显示返回按钮 -->
			<view v-if="nodeStatus === 'completed'" class="action-btn secondary-btn single-btn" @tap="goBack">
				<text class="btn-text">返回</text>
			</view>
			<!-- 未完成状态：显示返回和提交按钮 -->
			<template v-else>
				<view class="action-btn secondary-btn" @tap="goBack">
					<text class="btn-text">返回</text>
				</view>
				<view class="action-btn primary-btn" @tap="handleSubmit">
					<text class="btn-text">{{nodeStatus === 'processing' ? '更新' : '提交'}}</text>
				</view>
			</template>
		</view>
	</view>
</template>

<script>
	export default {
		name: 'FormIndex',
		data() {
			return {
				formType: '',
				nodeId: '',
				id: '', // 新增：节点ID
				state: '', // 新增：节点状态
				formTitle: '',
				nodeStatus: '',
				leadId: '',
				flowId: '', // 新增：流程ID
				level: '', // 新增：节点层级
				nodeIndex: '', // 新增：主节点索引
				childIndex: '', // 新增：子节点索引
				realName: '', // 新增：负责人姓名
				duration: '', // 新增：时长
				parentNodeId: '', // 新增：父节点ID
				childNodeId: '', // 新增：子节点ID
				formConfig: {
					sections: []
				},
				formData: {}
			}
		},
		onLoad(options) {
			console.log('=== 表单页接收到的所有参数 ===')
			console.log('原始参数对象:', options)
			console.log('参数详情:')

			// 获取并打印所有参数
			this.formType = options.formType || ''
			this.nodeId = options.nodeId || ''
			this.id = options.id || '' // 对应的id
			this.state = options.state || '' // 对应的state
			this.formTitle = decodeURIComponent(options.title || '表单填写')
			this.nodeStatus = options.status || 'pending'
			this.leadId = options.leadId || ''
			this.flowId = options.flowId || '' // 流程ID
			this.level = options.level || '' // 节点层级
			this.nodeIndex = options.nodeIndex || '' // 主节点索引
			this.childIndex = options.childIndex || '' // 子节点索引
			this.realName = options.realName || '' // 负责人姓名
			this.duration = options.duration || '' // 时长
			this.parentNodeId = options.parentNodeId || '' // 父节点ID
			this.childNodeId = options.childNodeId || '' // 子节点ID

			// 详细打印每个参数
			console.log('- formType (表单类型):', this.formType)
			console.log('- nodeId (节点ID):', this.nodeId)
			console.log('- id (对应的ID):', this.id)
			console.log('- state (对应的状态):', this.state)
			console.log('- title (标题):', this.formTitle)
			console.log('- status (节点状态):', this.nodeStatus)
			console.log('- leadId (线索ID):', this.leadId)
			console.log('- flowId (流程ID):', this.flowId)
			console.log('- level (节点层级):', this.level)
			console.log('- nodeIndex (主节点索引):', this.nodeIndex)
			console.log('- childIndex (子节点索引):', this.childIndex)
			console.log('- realName (负责人姓名):', this.realName)
			console.log('- duration (时长):', this.duration)
			console.log('- parentNodeId (父节点ID):', this.parentNodeId)
			console.log('- childNodeId (子节点ID):', this.childNodeId)
			console.log('=== 参数打印完毕 ===')
			console.log('🔍 重要说明: id参数传递的是父节点ID:', this.id)

			this.loadFormConfig()
			this.loadFormData()
			this.loadFormDetail() // 新增：加载表单详情
		},
		methods: {
			// 加载表单配置
			async loadFormConfig() {
				// 模拟API调用，实际应该从后端获取表单配置
				try {
					// const response = await this.$api.getFormConfig(this.formType)
					// this.formConfig = response.data

					// 模拟数据
					this.formConfig = this.getMockFormConfig(this.formType)
					this.initFormData()
				} catch (error) {
					console.error('加载表单配置失败:', error)
					uni.showToast({
						title: '加载表单配置失败',
						icon: 'none'
					})
				}
			},

			// 加载表单详情
			loadFormDetail() {
				console.log('=== 开始调用 EstateWork/flowdetail 接口 ===')
				console.log('请求参数:')
				console.log('- 传入的id参数(父节点ID):', this.id)
				console.log('- 父节点ID:', this.parentNodeId)
				console.log('- 子节点ID:', this.childNodeId)
				console.log('- 说明: 接口使用的是父节点ID')

				// 调用 EstateWork/flowdetail 接口，使用传入的id作为参数
				this.$api.request('EstateWork/flowdetail', {
					id: this.id // 使用传入的id参数
				}, (res) => {
					console.log('=== EstateWork/flowdetail 接口返回结果 ===')
					console.log('完整响应对象:', res)
					console.log('响应状态:', res.status)
					console.log('响应信息:', res.info || '无')

					if (res.status === 'ok') {


						// 打印所有返回字段
						console.log('🔍 所有返回字段:')
						Object.keys(res).forEach(key => {
							console.log(`  - ${key}:`, res[key])
						})

						// 处理动态表单配置 - 渲染所有项目为卡片
						if (res.flow && Array.isArray(res.flow)) {
							console.log('🔧 开始处理动态表单配置 - 渲染所有项目')
							this.processAllFlowItems(res.flow)
						} else {
							console.log('⚠️ 没有找到flow数据，使用默认表单配置')
						}

					} else {
						console.log('❌ 接口调用失败:', res.info || '未知错误')
						uni.showToast({
							title: res.info || 'EstateWork/flowdetail 接口调用失败',
							icon: 'none',
							duration: 3000
						})
					}

					console.log('=== EstateWork/flowdetail 接口调用完毕 ===')
				}, (error) => {
					console.error('=== EstateWork/flowdetail 接口调用出错 ===')
					console.error('❌ 错误信息:', error)
					uni.showToast({
						title: 'EstateWork/flowdetail 接口调用出错',
						icon: 'none',
						duration: 3000
					})
				})
			},

			// 处理所有流程项目 - 每个节点作为一个卡片
			processAllFlowItems(flowData) {
				console.log('=== 开始处理所有流程项目 ===')
				console.log('原始flow数据:', flowData)

				const sections = []

				flowData.forEach((node, nodeIndex) => {
					console.log(`处理第${nodeIndex + 1}个节点:`, node)
					console.log(`- 节点ID: ${node.id}`)
					console.log(`- 节点名称: ${node.work_name}`)
					console.log(`- 节点状态: ${node.state}`)
					console.log(`- 子表单项数量: ${node.children ? node.children.length : 0}`)

					// 为每个节点创建一个独立的卡片
					if (node.children && Array.isArray(node.children) && node.children.length > 0) {
						const section = {
							title: node.work_name, // 使用节点名称作为卡片标题
							nodeId: node.id,
							nodeState: node.state,
							fullName: node.full_name,
							realName: node.real_name,
							fields: []
						}

						// 处理该节点下的所有表单项
						node.children.forEach((child, childIndex) => {
							console.log(`  处理子表单项 ${childIndex + 1}:`, child)

							const field = {
								key: `field_${child.id}`,
								label: child.work_name,
								type: this.convertFormType(child.form_type),
								placeholder: this.getPlaceholder(child.work_name, child.form_type),
								required: child.require === 2,
								apiId: child.id,
								fullName: child.full_name,
								formId: child.form_id,
								originalFormType: child.form_type,
								state: child.state,
								val: child.val,
								valFile: child.val_file,
								nodeId: node.id, // 关联到父节点
								parentNodeName: node.work_name
							}

							// 设置默认值
							if (child.val) {
								field.defaultValue = child.val
							}
							if (child.val_file) {
								field.defaultValue = child.val_file
							}

							section.fields.push(field)
							console.log(`    已添加字段:`, field)
						})

						sections.push(section)
						console.log(`已添加卡片: ${section.title}，包含 ${section.fields.length} 个字段`)
					} else {
						console.log(`节点 ${node.work_name} 没有子表单项，跳过`)
					}
				})

				// 更新表单配置
				this.formConfig = {
					sections
				}

				// 重新初始化表单数据
				this.initFormData()

				console.log('最终表单配置:', this.formConfig)
				console.log(`✅ 共生成 ${sections.length} 个卡片`)

				uni.showToast({
					title: `已加载 ${sections.length} 个表单卡片`,
					icon: 'success'
				})

				console.log('=== 所有流程项目处理完毕 ===')
			},

			// 处理动态表单配置
			processDynamicFormConfig(flowData) {
				console.log('=== 开始处理动态表单配置 ===')
				console.log('原始flow数据:', flowData)

				// 查找匹配的节点（根据传入的父节点ID）
				const targetNode = flowData.find(node => node.id == this.id)

				if (!targetNode) {
					console.log('❌ 未找到匹配的节点，ID:', this.id)
					uni.showToast({
						title: '未找到对应的表单配置',
						icon: 'none'
					})
					return
				}

				console.log('✅ 找到匹配的节点:', targetNode)
				console.log('节点名称:', targetNode.work_name)
				console.log('节点状态:', targetNode.state)
				console.log('节点ID:', targetNode.id)
				console.log('节点层级:', targetNode.level)
				console.log('子表单项数量:', targetNode.children ? targetNode.children.length : 0)
				console.log('子表单项详情:', targetNode.children)

				// 更新表单标题
				this.formTitle = targetNode.work_name || this.formTitle

				// 处理子表单项
				if (targetNode.children && Array.isArray(targetNode.children)) {
					const dynamicFormConfig = this.convertToFormConfig(targetNode.children)
					console.log('转换后的表单配置:', dynamicFormConfig)

					// 更新表单配置
					this.formConfig = dynamicFormConfig

					// 重新初始化表单数据
					this.initFormData()

					console.log('✅ 动态表单配置已应用')
					uni.showToast({
						title: '表单配置已加载',
						icon: 'success'
					})
				} else {
					console.log('⚠️ 该节点没有子表单项')
					uni.showToast({
						title: '该节点没有表单项',
						icon: 'none'
					})
				}

				console.log('=== 动态表单配置处理完毕 ===')
			},

			// 将API返回的children数据转换为表单配置格式
			convertToFormConfig(children) {
				console.log('=== 开始转换表单配置 ===')

				const sections = [{
					title: '表单信息',
					fields: []
				}]

				children.forEach((child, index) => {
					console.log(`处理第${index + 1}个表单项:`, child)
					console.log(`- ID: ${child.id}`)
					console.log(`- 名称: ${child.work_name}`)
					console.log(`- 类型: ${child.form_type}`)
					console.log(`- 必填: ${child.require}`)
					console.log(`- 状态: ${child.state}`)

					const field = {
						key: `field_${child.id}`, // 使用ID作为key
						label: child.work_name, // 使用work_name作为标签
						type: this.convertFormType(child.form_type), // 使用API返回的form_type
						placeholder: this.getPlaceholder(child.work_name, child.form_type),
						required: child.require === 2, // require=2表示必填
						apiId: child.id, // 保存API中的ID
						fullName: child.full_name, // 保存完整名称
						formId: child.form_id, // 保存表单ID
						originalFormType: child.form_type, // 保存原始表单类型
						state: child.state, // 保存状态
						val: child.val, // 保存已填写的值
						valFile: child.val_file // 保存文件值
					}

					// 根据form_type设置特殊配置
					if (child.form_type === 'text') {
						field.inputType = 'text'
					} else if (child.form_type === 'number') {
						field.inputType = 'number'
					} else if (child.form_type === 'image' || child.form_type === 'file') {
						field.type = 'image'
					} else if (child.form_type === 'video') {
						field.type = 'video'
					}

					// 如果已有值，设置默认值
					if (child.val) {
						field.defaultValue = child.val
					}
					if (child.val_file) {
						field.defaultValue = child.val_file
					}

					sections[0].fields.push(field)
					console.log(`已添加字段:`, field)
				})

				console.log('=== 表单配置转换完毕 ===')
				return {
					sections
				}
			},

			// 转换API返回的form_type为组件支持的类型
			convertFormType(formType) {
				console.log('转换表单类型:', formType)

				const typeMap = {
					'text': 'input',
					'number': 'number',
					'textarea': 'textarea',
					'select': 'picker',
					'date': 'date',
					'image': 'image',
					'file': 'image', // 文件类型暂时当作图片处理
					'video': 'video'
				}

				const convertedType = typeMap[formType] || 'input'
				console.log(`${formType} -> ${convertedType}`)
				return convertedType
			},

			// 根据字段名称和类型生成占位符
			getPlaceholder(workName, formType) {
				if (formType === 'image' || formType === 'file') {
					return `请上传${workName}`
				} else if (formType === 'video') {
					return `请上传${workName}`
				} else if (formType === 'select') {
					return `请选择${workName}`
				} else if (formType === 'date') {
					return `请选择${workName}`
				} else if (formType === 'textarea') {
					return `请输入${workName}详细信息`
				} else {
					return `请输入${workName}`
				}
			},

			// 根据字段名称推断字段类型（备用方法）
			getFieldType(workName) {
				if (workName.includes('照片') || workName.includes('图')) {
					return 'image'
				} else if (workName.includes('视频')) {
					return 'video'
				} else if (workName.includes('面宽') || workName.includes('进深') || workName.includes('数量')) {
					return 'number'
				} else if (workName.includes('描述') || workName.includes('说明') || workName.includes('备注')) {
					return 'textarea'
				} else {
					return 'input'
				}
			},

			// 加载表单数据
			async loadFormData() {
				if (this.nodeStatus === 'completed' || this.nodeStatus === 'processing') {
					try {
						// const response = await this.$api.getFormData(this.nodeId)
						// this.formData = response.data

						// 模拟已填写的数据
						this.formData = this.getMockFormData(this.formType)
					} catch (error) {
						console.error('加载表单数据失败:', error)
					}
				}
			},

			// 初始化表单数据
			initFormData() {
				console.log('=== 初始化表单数据 ===')
				const data = {}

				this.formConfig.sections.forEach(section => {
					section.fields.forEach(field => {
						if (!data.hasOwnProperty(field.key)) {
							// 优先使用API返回的值
							let defaultValue = ''
							if (field.val) {
								defaultValue = field.val
							} else if (field.valFile) {
								defaultValue = field.valFile
							} else if (field.defaultValue) {
								defaultValue = field.defaultValue
							}

							data[field.key] = defaultValue
							console.log(`初始化字段 ${field.key} (${field.label}):`, defaultValue)
						}
					})
				})

				this.formData = {
					...data,
					...this.formData
				}
				console.log('初始化后的表单数据:', this.formData)
				console.log('=== 表单数据初始化完毕 ===')
			},

			// 获取模拟表单配置
			getMockFormConfig(formType) {
				const configs = {
					requirement_survey: {
						sections: [{
								title: '客户基本信息',
								fields: [{
										key: 'customerName',
										label: '客户姓名',
										type: 'input',
										placeholder: '请输入客户姓名',
										required: true
									},
									{
										key: 'customerPhone',
										label: '联系电话',
										type: 'input',
										inputType: 'number',
										placeholder: '请输入联系电话',
										required: true
									},
									{
										key: 'customerType',
										label: '客户类型',
										type: 'picker',
										placeholder: '请选择客户类型',
										options: [{
												label: '个人客户',
												value: 'personal'
											},
											{
												label: '企业客户',
												value: 'enterprise'
											}
										],
										required: true
									}
								]
							},
							{
								title: '需求信息',
								fields: [{
										key: 'requirements',
										label: '具体需求',
										type: 'textarea',
										placeholder: '请详细描述客户需求',
										required: true,
										maxLength: 1000
									},
									{
										key: 'budget',
										label: '预算范围',
										type: 'picker',
										placeholder: '请选择预算范围',
										options: [{
												label: '5万以下',
												value: 'below_5w'
											},
											{
												label: '5-10万',
												value: '5w_10w'
											},
											{
												label: '10-20万',
												value: '10w_20w'
											},
											{
												label: '20万以上',
												value: 'above_20w'
											}
										],
										required: true
									},
									{
										key: 'timeline',
										label: '期望完成时间',
										type: 'date',
										placeholder: '请选择期望完成时间',
										required: true
									}
								]
							}
						]
					},
					technical_solution: {
						sections: [{
								title: '技术方案概述',
								fields: [{
										key: 'solutionName',
										label: '方案名称',
										type: 'input',
										placeholder: '请输入技术方案名称',
										required: true
									},
									{
										key: 'technology',
										label: '核心技术',
										type: 'picker',
										placeholder: '请选择核心技术',
										options: [{
												label: '物联网技术',
												value: 'iot'
											},
											{
												label: '人工智能',
												value: 'ai'
											},
											{
												label: '云计算',
												value: 'cloud'
											},
											{
												label: '大数据',
												value: 'bigdata'
											}
										],
										required: true
									},
									{
										key: 'description',
										label: '方案描述',
										type: 'textarea',
										placeholder: '请详细描述技术方案',
										required: true,
										maxLength: 2000
									}
								]
							},
							{
								title: '实施计划',
								fields: [{
										key: 'duration',
										label: '实施周期',
										type: 'picker',
										placeholder: '请选择实施周期',
										options: [{
												label: '1-2周',
												value: '1_2_weeks'
											},
											{
												label: '3-4周',
												value: '3_4_weeks'
											},
											{
												label: '1-2个月',
												value: '1_2_months'
											},
											{
												label: '3个月以上',
												value: 'above_3_months'
											}
										],
										required: true
									},
									{
										key: 'resources',
										label: '所需资源',
										type: 'textarea',
										placeholder: '请描述所需的人力、物力资源',
										required: true
									}
								]
							}
						]
					},
					quotation: {
						sections: [{
								title: '报价信息',
								fields: [{
										key: 'projectName',
										label: '项目名称',
										type: 'input',
										placeholder: '请输入项目名称',
										required: true
									},
									{
										key: 'totalAmount',
										label: '总金额',
										type: 'number',
										placeholder: '请输入总金额（元）',
										required: true
									},
									{
										key: 'paymentMethod',
										label: '付款方式',
										type: 'picker',
										placeholder: '请选择付款方式',
										options: [{
												label: '一次性付款',
												value: 'full_payment'
											},
											{
												label: '分期付款',
												value: 'installment'
											},
											{
												label: '按进度付款',
												value: 'progress_payment'
											}
										],
										required: true
									}
								]
							},
							{
								title: '费用明细',
								fields: [{
										key: 'materialCost',
										label: '材料费用',
										type: 'number',
										placeholder: '请输入材料费用（元）',
										required: true
									},
									{
										key: 'laborCost',
										label: '人工费用',
										type: 'number',
										placeholder: '请输入人工费用（元）',
										required: true
									},
									{
										key: 'otherCost',
										label: '其他费用',
										type: 'number',
										placeholder: '请输入其他费用（元）'
									},
									{
										key: 'remarks',
										label: '备注说明',
										type: 'textarea',
										placeholder: '请输入费用说明或备注',
										maxLength: 500
									}
								]
							}
						]
					},
					risk_assessment: {
						sections: [{
								title: '风险识别',
								fields: [{
										key: 'riskType',
										label: '风险类型',
										type: 'picker',
										placeholder: '请选择主要风险类型',
										options: [{
												label: '技术风险',
												value: 'technical'
											},
											{
												label: '进度风险',
												value: 'schedule'
											},
											{
												label: '成本风险',
												value: 'cost'
											},
											{
												label: '质量风险',
												value: 'quality'
											}
										],
										required: true
									},
									{
										key: 'riskLevel',
										label: '风险等级',
										type: 'picker',
										placeholder: '请选择风险等级',
										options: [{
												label: '低风险',
												value: 'low'
											},
											{
												label: '中风险',
												value: 'medium'
											},
											{
												label: '高风险',
												value: 'high'
											}
										],
										required: true
									},
									{
										key: 'riskDescription',
										label: '风险描述',
										type: 'textarea',
										placeholder: '请详细描述识别到的风险',
										required: true,
										maxLength: 1000
									}
								]
							},
							{
								title: '应对措施',
								fields: [{
										key: 'preventiveMeasures',
										label: '预防措施',
										type: 'textarea',
										placeholder: '请描述预防风险的措施',
										required: true
									},
									{
										key: 'contingencyPlan',
										label: '应急预案',
										type: 'textarea',
										placeholder: '请描述风险发生时的应急预案',
										required: true
									}
								]
							}
						]
					}
				}

				return configs[formType] || {
					sections: []
				}
			},

			// 获取模拟表单数据
			getMockFormData(formType) {
				const mockData = {
					requirement_survey: {
						customerName: '张先生',
						customerPhone: '13812345678',
						customerType: 'personal',
						requirements: '希望安装智能家居系统，包括智能照明、安防监控、环境控制等功能',
						budget: '10w_20w',
						timeline: '2024-03-01'
					},
					technical_solution: {
						solutionName: '智能家居集成方案',
						technology: 'iot',
						description: '基于物联网技术的智能家居解决方案，包括智能照明、安防监控、环境控制等子系统',
						duration: '1_2_months',
						resources: '需要技术工程师2名，项目经理1名，安装人员3名'
					},
					quotation: {
						projectName: '张先生智能家居项目',
						totalAmount: '150000',
						paymentMethod: 'progress_payment',
						materialCost: '80000',
						laborCost: '50000',
						otherCost: '20000',
						remarks: '包含设备采购、安装调试、培训服务等'
					},
					risk_assessment: {
						riskType: 'technical',
						riskLevel: 'medium',
						riskDescription: '客户对智能家居技术了解有限，可能在使用过程中遇到操作困难',
						preventiveMeasures: '提供详细的用户手册和操作培训，建立客服支持体系',
						contingencyPlan: '安排技术人员定期回访，提供远程技术支持服务'
					}
				}

				return mockData[formType] || {}
			},

			// 获取状态文本
			getStatusText(status) {
				const statusMap = {
					'pending': '待开启',
					'processing': '进行中',
					'completed': '已完成',
					'disabled': '禁用'
				}
				return statusMap[status] || '未知'
			},

			// 获取状态样式类名
			getStateClass(state) {
				const stateClassMap = {
					'已完成': 'completed',
					'进行中': 'processing',
					'待开启': 'pending',
					'已提交': 'processing',
					'待提交': 'pending',
					'已驳回': 'rejected',
					'已废弃': 'abandoned'
				}
				return stateClassMap[state] || 'default'
			},

			// 选择器变化处理
			handlePickerChange(event, fieldKey) {
				const index = event.detail.value
				const field = this.findFieldByKey(fieldKey)
				if (field && field.options && field.options[index]) {
					this.formData[fieldKey] = field.options[index].value
				}
			},

			// 日期变化处理
			handleDateChange(event, fieldKey) {
				this.formData[fieldKey] = event.detail.value
			},

			// 获取选择器显示文本
			getPickerDisplayText(field, value) {
				if (!value || !field.options) return ''
				const option = field.options.find(opt => opt.value === value)
				return option ? option.label : ''
			},

			// 根据key查找字段配置
			findFieldByKey(key) {
				for (let section of this.formConfig.sections) {
					for (let field of section.fields) {
						if (field.key === key) {
							return field
						}
					}
				}
				return null
			},

			// 表单验证
			validateForm() {
				for (let section of this.formConfig.sections) {
					for (let field of section.fields) {
						if (field.required && !this.formData[field.key]) {
							uni.showToast({
								title: `请填写${field.label}`,
								icon: 'none'
							})
							return false
						}
					}
				}
				return true
			},

			// 提交表单
			async handleSubmit() {
				if (!this.validateForm()) {
					return
				}

				try {
					uni.showLoading({
						title: '提交中...'
					})

					// 模拟API调用
					// await this.$api.submitForm({
					//   nodeId: this.nodeId,
					//   formType: this.formType,
					//   leadId: this.leadId,
					//   formData: this.formData
					// })

					// 模拟延迟
					await new Promise(resolve => setTimeout(resolve, 1000))

					uni.hideLoading()
					uni.showToast({
						title: '提交成功',
						icon: 'success'
					})

					// 延迟返回上一页
					setTimeout(() => {
						uni.navigateBack()
					}, 1500)

				} catch (error) {
					uni.hideLoading()
					console.error('提交表单失败:', error)
					uni.showToast({
						title: '提交失败，请重试',
						icon: 'none'
					})
				}
			},

			// 获取图片列表
			getImageList(fieldKey) {
				const value = this.formData[fieldKey]
				if (!value) return []
				if (Array.isArray(value)) return value
				if (typeof value === 'string') return value ? [value] : []
				return []
			},

			// 选择图片
			chooseImage(fieldKey) {
				const currentImages = this.getImageList(fieldKey)
				const remainingCount = 9 - currentImages.length

				if (remainingCount <= 0) {
					uni.showToast({
						title: '最多只能上传9张图片',
						icon: 'none'
					})
					return
				}

				uni.chooseImage({
					count: remainingCount,
					sizeType: ['compressed'],
					sourceType: ['album', 'camera'],
					success: (res) => {
						console.log('选择图片成功:', res)
						const newImages = [...currentImages, ...res.tempFilePaths]
						this.formData[fieldKey] = newImages

						uni.showToast({
							title: `已添加 ${res.tempFilePaths.length} 张图片`,
							icon: 'success'
						})

						// 这里可以添加上传到服务器的逻辑
						// this.uploadImages(res.tempFilePaths, fieldKey)
					},
					fail: (err) => {
						console.error('选择图片失败:', err)
						uni.showToast({
							title: '选择图片失败',
							icon: 'none'
						})
					}
				})
			},

			// 预览图片
			previewImage(imagePath, imageList) {
				const urls = imageList || [imagePath]
				uni.previewImage({
					urls: urls,
					current: imagePath
				})
			},

			// 删除指定索引的图片
			removeImageByIndex(fieldKey, imageIndex) {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这张图片吗？',
					success: (res) => {
						if (res.confirm) {
							const currentImages = this.getImageList(fieldKey)
							currentImages.splice(imageIndex, 1)
							this.formData[fieldKey] = currentImages.length > 0 ? currentImages : []

							uni.showToast({
								title: '图片已删除',
								icon: 'success'
							})
						}
					}
				})
			},

			// 删除图片（保留兼容性）
			removeImage(fieldKey) {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除所有图片吗？',
					success: (res) => {
						if (res.confirm) {
							this.formData[fieldKey] = []
						}
					}
				})
			},

			// 选择视频
			chooseVideo(fieldKey) {
				uni.chooseVideo({
					sourceType: ['album', 'camera'],
					maxDuration: 60,
					camera: 'back',
					success: (res) => {
						console.log('选择视频成功:', res)
						this.formData[fieldKey] = res.tempFilePath
						// 这里可以添加上传到服务器的逻辑
						// this.uploadVideo(res.tempFilePath, fieldKey)
					},
					fail: (err) => {
						console.error('选择视频失败:', err)
						uni.showToast({
							title: '选择视频失败',
							icon: 'none'
						})
					}
				})
			},

			// 删除视频
			removeVideo(fieldKey) {
				uni.showModal({
					title: '确认删除',
					content: '确定要删除这个视频吗？',
					success: (res) => {
						if (res.confirm) {
							this.formData[fieldKey] = ''
						}
					}
				})
			},

			// 返回
			goBack() {
				uni.navigateBack()
			}
		}
	}
</script>

<style lang="scss" scoped>
	.container {
		background-color: #f5f5f5;
		min-height: 100vh;
		padding-bottom: 180rpx;
	}

	.header {
		background-color: #ffffff;
		padding: 30rpx;
		border-bottom: 1rpx solid #e5e5e5;
		display: flex;
		justify-content: space-between;
		align-items: center;
	}

	.header-title {
		font-size: 36rpx;
		font-weight: bold;
		color: #333333;
		flex: 1;
	}

	.status-badge {
		padding: 8rpx 16rpx;
		border-radius: 20rpx;

		&.status-pending {
			background-color: #fff7e6;
		}

		&.status-processing {
			background-color: #e6f3ff;
		}

		&.status-completed {
			background-color: #e8f5e8;
		}
	}

	.status-text {
		font-size: 24rpx;
		font-weight: 500;

		.status-pending & {
			color: #ff9500;
		}

		.status-processing & {
			color: #007aff;
		}

		.status-completed & {
			color: #34c759;
		}
	}

	.form-container {
		padding: 20rpx;
	}

	.form-section {
		background-color: #ffffff;
		border-radius: 12rpx;
		margin-bottom: 20rpx;
		overflow: hidden;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	}

	.section-header {
		background-color: #f8f9fa;
		border-bottom: 1rpx solid #e5e5e5;
		padding: 30rpx;
	}

	.section-title {
		font-size: 32rpx;
		font-weight: bold;
		color: #333333;
		margin-bottom: 16rpx;
	}

	.section-info {
		display: flex;
		align-items: center;
		gap: 16rpx;
		margin-bottom: 12rpx;
	}

	.node-state {
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		font-size: 24rpx;
		font-weight: 500;

		&.state-completed {
			color: #34c759;
			background-color: #e8f5e8;
		}

		&.state-processing {
			color: #007aff;
			background-color: #e6f3ff;
		}

		&.state-pending {
			color: #ff9500;
			background-color: #fff7e6;
		}

		&.state-rejected {
			color: #ff3b30;
			background-color: #ffe6e6;
		}

		&.state-abandoned {
			color: #636366;
			background-color: #f0f0f0;
		}

		&.state-default {
			color: #666666;
			background-color: #f5f5f5;
		}
	}

	.node-id {
		font-size: 22rpx;
		color: #999999;
		background-color: #f0f0f0;
		padding: 2rpx 8rpx;
		border-radius: 8rpx;
	}

	.section-responsible {
		display: flex;
		align-items: center;
	}

	.responsible-label {
		font-size: 26rpx;
		color: #666666;
	}

	.responsible-name {
		font-size: 26rpx;
		color: #007aff;
		font-weight: 500;
	}

	.form-fields {
		padding: 30rpx;
	}

	.field-item {
		margin-bottom: 40rpx;

		&:last-child {
			margin-bottom: 0;
		}
	}

	.field-wrapper {
		display: flex;
		flex-direction: column;
	}

	.field-label {
		font-size: 32rpx;
		color: #333333;
		margin-bottom: 16rpx;
		font-weight: 600;
		line-height: 1.4;
	}

	.required-mark {
		color: #ff3b30;
		margin-left: 4rpx;
	}

	.field-input {
		padding: 0rpx 24rpx;
		border: 2rpx solid #e5e5e5;
		border-radius: 12rpx;
		font-size: 30rpx;
		color: #333333;
		background-color: #ffffff;
		min-height: 88rpx;
		line-height: 1.5;
		box-sizing: border-box;
		width: 100%;
		transition: all 0.3s ease;

		&:focus {
			border-color: #007aff;
			background-color: #fafbff;
			box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
		}

		&:disabled {
			background-color: #f5f5f5;
			color: #999999;
			border-color: #d1d1d6;
		}

		&::placeholder {
			color: #999999;
			font-size: 28rpx;
		}
	}

	.field-textarea {
		padding: 28rpx 24rpx;
		border: 2rpx solid #e5e5e5;
		border-radius: 12rpx;
		font-size: 30rpx;
		color: #333333;
		background-color: #ffffff;
		min-height: 160rpx;
		line-height: 1.6;
		box-sizing: border-box;
		width: 100%;
		resize: none;
		transition: all 0.3s ease;

		&:focus {
			border-color: #007aff;
			background-color: #fafbff;
			box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
		}

		&:disabled {
			background-color: #f5f5f5;
			color: #999999;
			border-color: #d1d1d6;
		}

		&::placeholder {
			color: #999999;
			font-size: 28rpx;
		}
	}

	.field-picker {
		padding: 28rpx 24rpx;
		border: 2rpx solid #e5e5e5;
		border-radius: 12rpx;
		background-color: #ffffff;
		display: flex;
		justify-content: space-between;
		align-items: center;
		min-height: 88rpx;
		box-sizing: border-box;
		transition: all 0.3s ease;

		&:active {
			border-color: #007aff;
			background-color: #fafbff;
		}

		&.disabled {
			background-color: #f5f5f5;
			border-color: #d1d1d6;

			.picker-text {
				color: #999999;
			}

			.picker-arrow {
				color: #cccccc;
			}
		}
	}

	.picker-text {
		font-size: 30rpx;
		color: #333333;
		flex: 1;
		line-height: 1.5;

		&.placeholder {
			color: #999999;
			font-size: 28rpx;
		}
	}

	.picker-arrow {
		font-size: 28rpx;
		color: #999999;
		margin-left: 16rpx;
	}

	.footer-actions {
		position: fixed;
		bottom: 0;
		left: 0;
		right: 0;
		background-color: #ffffff;
		padding: 30rpx 40rpx 40rpx 40rpx;
		border-top: 2rpx solid #e5e5e5;
		display: flex;
		gap: 24rpx;
		box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.15);
		z-index: 1000;
	}

	.action-btn {
		flex: 1;
		padding: 36rpx 24rpx;
		border-radius: 16rpx;
		text-align: center;
		min-height: 96rpx;
		display: flex;
		align-items: center;
		justify-content: center;
		transition: all 0.3s ease;
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);

		&:active {
			opacity: 0.8;
			transform: scale(0.98);
		}

		&.single-btn {
			width: 100%;
			flex: none;
		}
	}

	.primary-btn {
		background-color: #1976d2;
		background: linear-gradient(135deg, #1976d2 0%, #1565c0 100%);

		&:hover {
			background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
		}
	}

	.secondary-btn {
		background-color: #87ceeb;
		background: linear-gradient(135deg, #87ceeb 0%, #5dade2 100%);

		&:hover {
			background: linear-gradient(135deg, #5dade2 0%, #3498db 100%);
		}
	}

	.success-btn {
		background-color: #28a745;
		background: linear-gradient(135deg, #28a745 0%, #218838 100%);

		&:hover {
			background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
		}
	}

	.btn-text {
		color: #ffffff;
		font-size: 36rpx;
		font-weight: 600;
		letter-spacing: 1rpx;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
	}

	/* 图片上传样式 */
	.image-upload-container {
		border: 1rpx solid #e5e5e5;
		border-radius: 8rpx;
		padding: 20rpx;
		background-color: #fafafa;
	}

	.image-list {
		display: flex;
		flex-wrap: wrap;
		gap: 16rpx;
	}

	.image-item {
		position: relative;
		width: 200rpx;
		height: 200rpx;
		border-radius: 8rpx;
		overflow: hidden;
		background-color: #f5f5f5;
	}

	.thumbnail-image {
		width: 100%;
		height: 100%;
		border-radius: 8rpx;
	}

	.image-delete {
		position: absolute;
		top: 8rpx;
		right: 8rpx;
		width: 40rpx;
		height: 40rpx;
		background-color: rgba(0, 0, 0, 0.6);
		border-radius: 50%;
		display: flex;
		align-items: center;
		justify-content: center;

		&:active {
			background-color: rgba(0, 0, 0, 0.8);
		}
	}

	.delete-icon {
		color: #ffffff;
		font-size: 32rpx;
		font-weight: bold;
		line-height: 1;
	}

	.add-image-btn {
		width: 200rpx;
		height: 200rpx;
		border: 2rpx dashed #d1d1d6;
		border-radius: 8rpx;
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		background-color: #ffffff;

		&:active {
			background-color: #f0f0f0;
		}
	}

	.add-icon {
		font-size: 48rpx;
		color: #999999;
		margin-bottom: 8rpx;
	}

	.add-text {
		font-size: 24rpx;
		color: #999999;
	}

	.image-count {
		margin-top: 16rpx;
		text-align: center;
	}

	.count-text {
		font-size: 24rpx;
		color: #666666;
	}

	.image-preview {
		position: relative;
	}

	.preview-image {
		width: 100%;
		height: 300rpx;
		background-color: #f5f5f5;
	}

	.image-actions {
		display: flex;
		justify-content: space-between;
		padding: 16rpx;
		background-color: #f8f9fa;
		border-top: 1rpx solid #e5e5e5;
	}

	.action-btn {
		padding: 8rpx 16rpx;
		border-radius: 4rpx;
		font-size: 24rpx;
		color: #007aff;
		background-color: #e6f3ff;

		&.delete-btn {
			color: #ff3b30;
			background-color: #ffe6e6;
            height: 50rpx !important;
            min-height: 60rpx !important;
		}

		&:active {
			opacity: 0.8;
		}
	}

	.upload-placeholder {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		padding: 60rpx 20rpx;
		background-color: #f8f9fa;
		border: 2rpx dashed #d1d1d6;

		&:active {
			background-color: #f0f0f0;
		}
	}

	.upload-icon {
		font-size: 48rpx;
		margin-bottom: 16rpx;
	}

	.upload-text {
		font-size: 26rpx;
		color: #666666;
		text-align: center;
	}

	/* 视频上传样式 */
	.video-upload-container {
		border: 1rpx solid #e5e5e5;
		border-radius: 8rpx;
		overflow: hidden;
	}

	.video-preview {
		position: relative;
	}

	.preview-video {
		width: 100%;
		height: 300rpx;
		background-color: #f5f5f5;
	}

	.video-actions {
		display: flex;
		justify-content: flex-end;
		padding: 16rpx;
		background-color: #f8f9fa;
		border-top: 1rpx solid #e5e5e5;
	}
</style>